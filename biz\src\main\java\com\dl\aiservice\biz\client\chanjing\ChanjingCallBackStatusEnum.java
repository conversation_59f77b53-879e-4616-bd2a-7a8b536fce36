package com.dl.aiservice.biz.client.chanjing;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum ChanjingCallBackStatusEnum {

    /**
     * 视频合成任务回调
     */
    SUCCESS(30, "SUCCESS"),

    /**
     * 合成中
     */
    PROGRESS(10, "PROGRESS"),

    ERROR(-1, "ERROR");


    private final Integer code;
    private final String desc;

    ChanjingCallBackStatusEnum(Integer errorCode, String errorDesc) {
        this.code = errorCode;
        this.desc = errorDesc;
    }

    public static ChanjingCallBackStatusEnum getEnum(String desc) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getDesc(), desc)).findFirst().orElse(ChanjingCallBackStatusEnum.ERROR);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
