package com.dl.aiservice.biz.client.chanjing;


import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

import java.util.Arrays;
import java.util.Objects;

public enum ChanjingCallBackStatusEnum {

    /**
     * 视频合成任务回调
     */
    SUCCESS("30", "SUCCESS", 0),

    /**
     * 合成中
     */
    PROGRESS("10", "PROGRESS", 1),

    ERROR("-1", "ERROR", -1);


    private final String code;
    private final String desc;

    private final Integer statusCode;

    ChanjingCallBackStatusEnum(String code, String desc, Integer statusCode) {
        this.code = code;
        this.desc = desc;
        this.statusCode = statusCode;
    }

    public static ChanjingCallBackStatusEnum getEnum(String code) {
        return Arrays.stream(values()).filter(value -> Objects.equals(value.getCode(), code)).findFirst().orElse(ChanjingCallBackStatusEnum.ERROR);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getStatusCode() {
        return statusCode;
    }
}
