package com.dl.aiservice.biz.service.digital.chanjing.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.biz.common.constant.Const;
import com.dl.aiservice.biz.config.AiConfig;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.properties.cos.ChanjingProperties;
import com.dl.aiservice.biz.service.digital.AbstractDigitalService;
import com.dl.aiservice.biz.service.digital.DigitalDBService;
import com.dl.aiservice.biz.service.digital.bo.CreateVideoBO;
import com.dl.aiservice.biz.service.digital.bo.CreateVideoUpdateBO;
import com.dl.aiservice.biz.service.digital.chanjing.ChanjingDigitalService;
import com.dl.aiservice.biz.service.digital.dto.req.*;
import com.dl.aiservice.biz.service.digital.dto.resp.*;
import com.dl.aiservice.share.common.req.PageRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description：蝉镜数字人服务实现
 * @author： Pelot
 * @create： 2025/9/3 11:19
 */
@Slf4j
@Service
public class ChanjingDigitalServiceImpl extends AbstractDigitalService implements ChanjingDigitalService {

    @Resource
    private ChanjingProperties chanjingProperties;

    @Resource
    private DigitalDBService digitalDBService;

    @Resource
    private AiConfig aiConfig;

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;

    private static final String CALL_BACK_URL = "%s/chanjing/callback";

    // 蝉镜API接口地址
    private static final String ACCESS_TOKEN_URL = "https://open-api.chanjing.cc/open/v1/access_token";
    private static final String CREATE_VIDEO_URL = "https://open-api.chanjing.cc/open/v1/create_video";
    @Override
    public List<ServiceChannelEnum> getEnums() {
        return Lists.newArrayList(ServiceChannelEnum.CHAN_JING);
    }

    @Override
    public CreateResponseDTO videoCreate(CreateRequestDTO request) {
        log.info("蝉镜数字人视频创建开始，请求参数：{}", JSONUtil.toJsonStr(request));

        try {
            // 1. 获取access_token
            String accessToken = getAccessToken();

//            // 2. 保存任务到数据库
//            CreateVideoBO createVideoBO = new CreateVideoBO();
//            createVideoBO.setCallbackUrl(request.getCallbackUrl());
//            createVideoBO.setVideoName(request.getVideoName());
//            createVideoBO.setWorksBizId(request.getWorksBizId());
//            createVideoBO.setVideoTaskJobId(request.getVideoTaskJobId());
//
//            MediaProduceJobPO mediaProduceJobPO = digitalDBService.saveVideoCreate(createVideoBO);

            MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.lambdaQuery().eq(MediaProduceJobPO::getId, request.getUpdateId()).one();

            if (mediaProduceJobPO == null) {
                throw BusinessServiceException.getInstance("-1","未找到对应的任务");
            }
            // 3. 构建创建视频请求参数
            JSONObject videoRequest = buildCreateVideoRequest(request);

            // 4. 调用蝉镜创建视频接口
            String response = HttpUtil.createPost(CREATE_VIDEO_URL)
                    .header("access_token", accessToken)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(videoRequest))
                    .execute()
                    .body();

            log.info("蝉镜创建视频接口响应：{}", response);

            // 5. 解析响应
            JSONObject responseJson = JSONUtil.parseObj(response);
            if (responseJson.getInt("code") != 0) {
                throw BusinessServiceException.getInstance("-1","蝉镜创建视频失败：" + responseJson.getStr("msg"));
            }

            String taskId = responseJson.getStr("data");

            // 6. 更新数据库任务记录
            CreateVideoUpdateBO updateBO = new CreateVideoUpdateBO();
            updateBO.setId(mediaProduceJobPO.getId());
            updateBO.setTaskId(taskId);
            digitalDBService.updateVideoById(updateBO);

            // 7. 构建返回结果
            CreateResponseDTO responseDTO = new CreateResponseDTO();
            responseDTO.setTaskId(taskId);
            responseDTO.setMediaJobId(mediaProduceJobPO.getMediaJobId());
            responseDTO.setWorksBizId(request.getWorksBizId());

            log.info("蝉镜数字人视频创建成功，返回结果：{}", JSONUtil.toJsonStr(responseDTO));
            return responseDTO;

        } catch (Exception e) {
            log.error("蝉镜数字人视频创建失败", e);
            throw BusinessServiceException.getInstance("-1","蝉镜数字人视频创建失败：" + e.getMessage());
        }
    }

    @Override
    public ResultPageModel<RobotResponseDTO> robotPageList(PageRequestDTO pageRequestDTO) {
        return null;
    }

    @Override
    public RobotDetailResponseDTO robotDetail(RobotDetailRequestDTO requestDTO) {
        return null;
    }

    @Override
    public TrainResponseDTO getTrain(TrainRequestDTO gjTrainRequestDTO) {
        return null;
    }

    @Override
    public ProgressResponseDTO getProgress(TaskRequestDTO taskRequestDTO) {
        return null;
    }

    @Override
    public CreateTrainingResponseDTO createTraining(CreateTrainingRequestDTO request) {
        return null;
    }

    /**
     * 获取蝉镜access_token
     */
    private String getAccessToken() {
        try {
            JSONObject tokenRequest = new JSONObject();
            tokenRequest.put("app_id", chanjingProperties.getAppId());
            tokenRequest.put("secret_key", chanjingProperties.getSecretKey());

            String response = HttpUtil.createPost(ACCESS_TOKEN_URL)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(tokenRequest))
                    .execute()
                    .body();

            log.info("蝉镜获取token响应：{}", response);

            JSONObject responseJson = JSONUtil.parseObj(response);
            if (responseJson.getInt("code") != 0) {
                throw BusinessServiceException.getInstance("-1","获取蝉镜access_token失败：" + responseJson.getStr("msg"));
            }

            JSONObject data = responseJson.getJSONObject("data");
            return data.getStr("access_token");

        } catch (Exception e) {
            log.error("获取蝉镜access_token失败", e);
            throw BusinessServiceException.getInstance("-1","获取蝉镜access_token失败：" + e.getMessage());
        }
    }

    /**
     * 构建创建视频请求参数
     */
    private JSONObject buildCreateVideoRequest(CreateRequestDTO request) {
        JSONObject videoRequest = new JSONObject();

        // 构建person参数
        JSONObject person = new JSONObject();
        person.put("id", request.getSceneId()); // 使用sceneId作为数字人ID
        person.put("x", 0);
        person.put("y", 480);
        person.put("width", StringUtils.isNotBlank(request.getWidth()) ? Integer.parseInt(request.getWidth()) : 1080);
        person.put("height", StringUtils.isNotBlank(request.getHeight()) ? Integer.parseInt(request.getHeight()) : 1920);
        person.put("figure_type", "whole_body");
        videoRequest.put("person", person);

        // 构建audio参数
        JSONObject audio = new JSONObject();
        if (request.getType() == 1) { // 文本类型
            JSONObject tts = new JSONObject();
            tts.put("text", new String[]{request.getText()});
            tts.put("speed", request.getSpeed() != null ? request.getSpeed() : 1.0);
            tts.put("audio_man", request.getSpeakerId());
            audio.put("tts", tts);
            audio.put("type", "tts");
        } else { // 音频类型
            audio.put("wav_url", request.getAudioUrl());
            audio.put("type", "audio");
        }
        audio.put("volume", StringUtils.isNotBlank(request.getVolume()) ? Integer.parseInt(request.getVolume()) : 100);
        audio.put("language", "cn");
        videoRequest.put("audio", audio);

        // 设置背景色和屏幕尺寸
        videoRequest.put("bg_color", "#EDEDED");
        videoRequest.put("screen_width", StringUtils.isNotBlank(request.getWidth()) ? Integer.parseInt(request.getWidth()) : 1080);
        videoRequest.put("screen_height", StringUtils.isNotBlank(request.getHeight()) ? Integer.parseInt(request.getHeight()) : 1920);
        videoRequest.put("callback", String.format(CALL_BACK_URL, getCallbackPrefix()));
        return videoRequest;
    }

    private String getCallbackPrefix() {
        String callback = aiConfig.getCallbackPrefix();
        if (StringUtils.lastIndexOf(callback, Const.SLASH) == (callback.length() - Const.ONE)) {
            return StringUtils.substring(callback, Const.ZERO, callback.length() - Const.ONE);
        }
        return callback;
    }
}
