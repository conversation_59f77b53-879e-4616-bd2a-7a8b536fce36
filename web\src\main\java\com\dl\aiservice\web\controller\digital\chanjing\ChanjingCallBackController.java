package com.dl.aiservice.web.controller.digital.chanjing;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.biz.client.ivh.enums.IvhCallBackStatusEnum;
import com.dl.aiservice.biz.client.ivh.req.IvhBaseRequest;
import com.dl.aiservice.biz.dal.po.MediaProduceJobPO;
import com.dl.aiservice.biz.manager.MediaProduceJobManager;
import com.dl.aiservice.biz.manager.enums.MediaProduceJobTimeoutStatusEnum;
import com.dl.aiservice.biz.service.digital.dto.resp.DigitalVideoCallbackDTO;
import com.dl.aiservice.biz.service.digital.ivh.IvhDigitalService;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.web.controller.digital.chanjing.vo.ChanjingCallbackVO;
import com.dl.aiservice.web.controller.digital.ivh.vo.IvhCallbackVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description :
 */
@Slf4j
@RestController
@RequestMapping("/chanjing")
public class ChanjingCallBackController {

    @Resource
    private MediaProduceJobManager mediaProduceJobManager;
    @Resource
    private IvhDigitalService ivhDigitalService;

    /**
     * 合成任务回调接口
     */
    @PostMapping("/callback")
    public void videoCreateCallBack(@RequestBody ChanjingCallbackVO request) {
        log.info("腾讯云回调，回调参数：{}", JSONObject.toJSONString(request));
        MediaProduceJobPO mediaProduceJobPO = mediaProduceJobManager.getOne(Wrappers.<MediaProduceJobPO>lambdaUpdate()
                .in(MediaProduceJobPO::getChannel,
                        Lists.newArrayList(ServiceChannelEnum.IVH.getCode(), ServiceChannelEnum.FUJIA_IVH.getCode(), ServiceChannelEnum.DLSY_IVH.getCode()))
                .eq(MediaProduceJobPO::getExtJobId, request.getId()));
        if (Objects.isNull(mediaProduceJobPO)) {
            log.info("腾讯云回调,未找到对应合成任务，三方任务id:{},具体回调信息：{}", request.getId(),
                    JSONObject.toJSONString(request));
            return;
        }

        //是否已超时
        Boolean hasTimeout = !MediaProduceJobTimeoutStatusEnum.UN.getStatus()
                .equals(mediaProduceJobPO.getTimeoutStatus());

        this.fillMediaProduceJobStatus(request, mediaProduceJobPO, hasTimeout);
        mediaProduceJobPO.setMediaUrl(request.getVideoUrl());
        mediaProduceJobPO.setFailReason(request.getMsg());
        mediaProduceJobPO.setResponseDt(new Date());
        if (Objects.nonNull(request.getDuration())) {
            mediaProduceJobPO.setDuration(request.getDuration() * 1.0);
        }
        mediaProduceJobManager.updateById(mediaProduceJobPO);
        Long worksBizId = mediaProduceJobPO.getWorksBizId();
        DigitalVideoCallbackDTO digitalCallbackDTO = new DigitalVideoCallbackDTO();
        BeanUtils.copyProperties(mediaProduceJobPO, digitalCallbackDTO);

        //已超时则不回调业务
        if (hasTimeout) {
            return;
        }

        //回调业务
        ivhDigitalService.callBackBiz(mediaProduceJobPO.getTenantCode(), worksBizId, mediaProduceJobPO.getCallbackUrl(),
                digitalCallbackDTO, JSONUtil.toJsonStr(request));

    }

    private void fillMediaProduceJobStatus(ChanjingCallbackVO request, MediaProduceJobPO mediaProduceJobPO,
                                           Boolean hasTimeout) {
        IvhCallBackStatusEnum ivhCallBackStatusEnum = IvhCallBackStatusEnum.getEnum(request.getStatus());

        //先判断是否已经超时，若已超时，则修改timeout_status，不处理status
        if (hasTimeout) {
            if (IvhCallBackStatusEnum.SUCCESS.equals(ivhCallBackStatusEnum)) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_SUCCESS.getStatus());
                return;
            }
            if (IvhCallBackStatusEnum.FAIL.equals(ivhCallBackStatusEnum)) {
                mediaProduceJobPO.setTimeoutStatus(MediaProduceJobTimeoutStatusEnum.TIMEOUT_FAIL.getStatus());
                return;
            }
            return;
        }

        mediaProduceJobPO.setStatus(ivhCallBackStatusEnum.getCode());
    }

}
