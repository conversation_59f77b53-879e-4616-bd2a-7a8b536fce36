# AI Service Application Configuration
# 基础配置
spring:
  application:
    name: dl-aiservice
  profiles:
    active: dev

# 服务器配置
server:
  port: 8081
  servlet:
    context-path: /

# 日志配置
logging:
  level:
    root: INFO
    com.dl: INFO
    org.springframework: ERROR
    org.quartz: ERROR
    org.activiti: ERROR
    org.ehcache: ERROR
    org.springframework.transaction: ERROR
    org.apache.ibatis: DEBUG
# SM4加密盐值
sm4:
  salt: da024ustkidtclu3

---

# 生产环境配置
spring:
  profiles: prod
  datasource:
#    url: ************************************************************************************************************************************************************************************
#    username: pelot
    url: ***********************************************************************************************************************************************************
    username: root
    password: Dingli@0301
  redis:
    host: r-bp1rgz6nqi3c07bxfnpd.redis.rds.aliyuncs.com
    port: 6379
    password: r-bp1rgz6nqi3c07bxfn:Dingli@0301
    database: 0
  cloud:
    stream:
      kafka:
        binder:
          brokers: alikafka-pre-cn-fzh4egxzg023-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-fzh4egxzg023-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-fzh4egxzg023-3-vpc.alikafka.aliyuncs.com:9092
      bindings:
        digitalprogressconsumer:
          destination: digital-progress-topic
          group: digital-progress-consumer-group
          consumer:
            max-attempts: 3
        digitalprogressproducer:
          destination: digital-progress-topic
        voicetrainprogressconsumer:
          destination: voice-train-progress-topic
          group: voice-train-progress-consumer-group
          consumer:
            max-attempts: 3
        voicetrainprogressproducer:
          destination: voice-train-progress-topic

# 生产环境业务配置
wealth:
  aiservice:
    callbackPrefix: https://prod-domain.com
    callbackHost: prod-domain.com

# 生产环境数字人配置
digtal:
  ivh:
    train:
      appkey: prod_ivh_train_appkey
      secret: prod_ivh_train_secret
    video:
      appkey: prod_ivh_video_appkey
      secret: prod_ivh_video_secret
      concurrency: 10
  ifly:
    appid: prod_ifly_appid
    apiSecret: prod_ifly_api_secret
  fujia:
    ivh:
      video:
        appkey: prod_fujia_ivh_video_appkey
        secret: prod_fujia_ivh_video_secret
        concurrency: 5
  chanjing:
      video:
        appId: 811dcf3c
        secretKey: 85be1ccfcf26458999f8badd1f9ec15d
        concurrency: 5
  dlsy:
    ivh:
      video:
        appkey: prod_dlsy_ivh_video_appkey
        secret: prod_dlsy_ivh_video_secret
        concurrency: 5
  gj:
    appkey: prod_gj_appkey
    secret: prod_gj_secret
  bytedance:
    appId: prod_bytedance_appid
    accessToken: prod_bytedance_access_token

# 生产环境阿里云配置
dl:
  chanjing:
    appId: 811dcf3c
    secretKey: 85be1ccfcf26458999f8badd1f9ec15d
  aliyun:
    accessKeyId: prod_aliyun_access_key_id
    accessKeySecret: prod_aliyun_access_key_secret
    appKey: prod_aliyun_app_key
    cos:
      bucketId: pelotavatar
      region: oss-cn-shanghai
      accessKeyId: LTAI5tJSisnsWjGXMEYvf7Hb
      accessKeySecret: ******************************
      url-prefix: https://pelotavatar.oss-cn-shanghai.aliyuncs.com/

  # 生产环境字节跳动配置
  bytedance:
    api:
      accessKeyId: prod_bytedance_access_key_id
      secretAccessKey: prod_bytedance_secret_access_key
    voice:
      appId: 9956549218
      accessToken: qzW4hTT2q5bF1h0NgbeQwD_2uJRjDgHe
      volcano-tts:
        appId: prod_volcano_tts_app_id
        accessToken: prod_volcano_tts_access_token

  # 生产环境腾讯云配置
  tencentcloud:
    defaultRegion: ap-beijing
    api:
      appId: prod_tencent_app_id
      secretId: prod_tencent_secret_id
      secretKey: prod_tencent_secret_key
    cos:
      bucketId: prod-cos-bucket
      ivhBucketId: prod-ivh-cos-bucket
      region: ap-beijing
    asr:
      customizationId: 111
    tts:
      region: 111
      secretId: 111
      secretKey: 111
  asr:
    channel: 111

  # 生产环境Kimi配置
  kimi:
    apikey: prod_kimi_apikey
    model: moonshot-v1-32k
    respMaxToken: 2048

  # 生产环境文件临时路径
  fileTempPath: /root/backend/data/aiservice/temp
# 生产环境XXL-Job配置
#xxl:
#  job:
#    enable: true
#    admin:
#      addresses: http://prod-xxl-job-host:8080/xxl-job-admin
#    accessToken: prod_xxl_job_access_token
#    executor:
#      appname: dl-aiservice-executor
#      address:
#      ip:
#      port: 9999
#      logpath: /data/logs/xxl-job
#      logretentiondays: 30

# 生产环境视频制作配置
videoProduce:
  notifyUrl:
    aliyun: https://prod-domain.com/video/produce/callback/aliyun
