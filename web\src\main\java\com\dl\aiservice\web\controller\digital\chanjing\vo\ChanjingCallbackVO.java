package com.dl.aiservice.web.controller.digital.chanjing.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChanjingCallbackVO implements Serializable {
    private static final long serialVersionUID = -5040616107365918691L;
    /**
     * 视频id
     */
    @JsonProperty(value = "id")
    private Long id;
    /**
     * 状态，10,生成中；30成功； 4X参数异常；5X服务异常；
     */
    @JsonProperty(value = "status")
    private String status;
    /**
     * 任务进度 0-100
     */
    @JsonProperty(value = "progress")
    private String progress;
    /**
     * 异常或失败的错误信息
     */
    @JsonProperty(value = "msg")
    private String msg;
    /**
     * 视频播放地址
     */
    @JsonProperty(value = "video_url")
    private String videoUrl;
    /**
     * 字幕时间轴链接，需要下载该文件获取
     */
    @JsonProperty(value = "subtitle_data_url")
    private String subtitleDataUrl;
    /**
     * 视频生成时间，时间戳
     */
    @JsonProperty(value = "create_time")
    private Long createTime;

    /**
     * 视频预览图片
     */
    @JsonProperty(value = "previewUrl")
    private String previewUrl;

    /**
     * 视频时长（单位秒）
     */
    @JsonProperty(value = "duration")
    private Integer duration;
}
