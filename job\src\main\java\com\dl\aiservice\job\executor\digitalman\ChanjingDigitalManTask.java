package com.dl.aiservice.job.executor.digitalman;

import com.dl.aiservice.biz.digitaljobhandler.ChanjingDigitalManJobHandler;
import com.dl.aiservice.biz.digitaljobhandler.FuJiaIvhDigitalManJobHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-13 15:56
 */
@Component
public class ChanjingDigitalManTask {

    @Resource
    private ChanjingDigitalManJobHandler chanjingDigitalManJobHandler;

//    @XxlJob("chanjingDMJobHandler")
//    1/5 * * * * ?
    public void fujiaIvhDmVideoJob() {
        chanjingDigitalManJobHandler.handleDmVideoCreate();
    }
}
