package com.dl.aiservice.job.executor.digitalman;

import com.dl.aiservice.biz.digitaljobhandler.ChanjingDigitalManJobHandler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-13 15:56
 */
@Component
public class ChanjingDigitalManTask {

    @Resource
    private ChanjingDigitalManJobHandler chanjingDigitalManJobHandler;

    /**
     * 蝉静数字人视频任务处理
     * 每5秒执行一次，从第1秒开始
     * 原XxlJob cron: 1/5 * * * * ?
     */
    @Scheduled(cron = "1/5 * * * * ?")
    public void fujiaIvhDmVideoJob() {
        chanjingDigitalManJobHandler.handleDmVideoCreate();
    }
}
