package com.dl.aiservice;

import com.dl.aiservice.biz.common.annotation.BaseDao;
import com.dl.aiservice.biz.mq.AiChannels;
import com.dtflys.forest.springboot.annotation.ForestScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ForestScan(basePackages = "com.dl.aiservice.biz.client")
@MapperScan(annotationClass = BaseDao.class)
@EnableBinding({ AiChannels.class })
@EnableScheduling
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
